import { styled, useAsync } from '@topwrite/common';
import { useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, Spinner } from 'react-bootstrap';
import { BsChevronDown, BsChevronRight, BsTrash } from 'react-icons/bs';
import { GoPlus } from 'react-icons/go';
import Button from '../../components/button';
import Modal, { ModalProps } from '../../components/modal';
import useLocalStorageStateWithBook from '../../lib/use-local-storage-state-with-book';
import { useContext } from './context';
import { McpServer } from './use-mcp-servers';

// 自定义 Hook 用于管理工具启用状态
const useToolsToggle = (storageKey: string, allToolNames: string[]) => {
    const [enabledTools, setEnabledTools] = useLocalStorageStateWithBook<string[] | null>(storageKey, null);

    // 计算实际启用的工具数量
    const actualEnabledTools = enabledTools === null ? allToolNames : enabledTools.filter(toolName => allToolNames.includes(toolName));
    const enabledCount = actualEnabledTools.length;

    const toggleAllTools = (enabled: boolean) => {
        if (enabled) {
            // 启用所有工具，设置为 null（表示全部启用）
            setEnabledTools(null);
        } else {
            // 禁用所有工具，设置为空数组
            setEnabledTools([]);
        }
    };

    const toggleTool = (name: string) => {
        const currentEnabled = enabledTools === null ? allToolNames : enabledTools;

        if (currentEnabled.includes(name)) {
            // 如果当前是启用状态，则禁用（从启用列表中移除）
            const newEnabled = currentEnabled.filter(toolName => toolName !== name);
            setEnabledTools(newEnabled);
        } else {
            // 如果当前是禁用状态，则启用（添加到启用列表）
            const newEnabled = [...currentEnabled, name];
            // 如果新列表包含所有工具，则设置为 null（表示全部启用）
            if (newEnabled.length === allToolNames.length) {
                setEnabledTools(null);
            } else {
                setEnabledTools(newEnabled);
            }
        }
    };

    return {
        actualEnabledTools,
        enabledCount,
        toggleAllTools,
        toggleTool
    };
};

export default function ToolsConfigModal(props: Partial<ModalProps>) {
    const { mcpServers, addMcpServer } = useContext();

    const parseServer = function(json: string) {
        const parsed = JSON.parse(json);
        if (parsed.mcpServers && typeof parsed.mcpServers === 'object') {
            // 处理标准格式的JSON导入
            const serverNames = Object.keys(parsed.mcpServers);
            if (serverNames.length > 0) {
                const firstServerName = serverNames[0];
                const serverConfig = parsed.mcpServers[firstServerName];
                return {
                    name: firstServerName,
                    url: serverConfig.url || '',
                    type: serverConfig.type || 'streamable_http',
                    headers: serverConfig.headers || {}
                };
            }
        }
        throw new Error('JSON格式不正确，请确保包含mcpServers字段');
    };

    const footer = <div className='d-flex gap-2'>
        <Button
            variant='outline-secondary'
            onClick={async () => {
                const formData = await Modal.prompt<{ json: string }>({
                    title: '导入 MCP 服务器',
                    schema: {
                        type: 'object',
                        required: ['json'],
                        properties: {
                            json: {
                                type: 'string',
                                title: '服务器名称',
                            }
                        }
                    },
                    uiSchema: {
                        json: {
                            'ui:widget': 'textarea',
                            'ui:rows': 12,
                            'ui:label': false,
                            'ui:autofocus': true,
                            'ui:placeholder': `请粘贴JSON配置，例如：
{
  "mcpServers": {
    "server_name": {
      "url": "https://mcp.com/mcp",
    }
  }
}`
                        }
                    },
                    validate(formData, errors) {
                        try {
                            parseServer(formData.json);
                        } catch (e: any) {
                            errors.json.addError(e.message);
                        }
                        return errors;
                    }
                });

                if (formData) {
                    const data = parseServer(formData.json);
                    addMcpServer({
                        name: data.name,
                        url: data.url,
                        ...(data.type !== 'streamable_http' && { type: data.type }),
                        ...(Object.keys(data.headers).length > 0 && { headers: data.headers })
                    });
                }
            }}
            className='d-flex align-items-center'
        >
            <GoPlus className='me-1' />
            导入 MCP 服务器
        </Button>
        <Button
            variant='outline-primary'
            onClick={async () => {
                const data = await Modal.prompt<{
                    name: string;
                    url: string;
                    type: string;
                    headers: { key: string; value: string }[];
                }>({
                    title: '添加 MCP 服务器',
                    schema: {
                        type: 'object',
                        required: ['name', 'url'],
                        properties: {
                            name: {
                                type: 'string',
                                title: '服务器名称',
                            },
                            url: {
                                type: 'string',
                                title: '服务器地址',
                            },
                            type: {
                                type: 'string',
                                title: '连接类型',
                                enum: ['streamable_http', 'sse',],
                                enumNames: ['HTTP', 'SSE',],
                                default: 'streamable_http'
                            },
                            headers: {
                                type: 'array',
                                title: '请求头',
                                items: {
                                    type: 'object',
                                    properties: {
                                        key: {
                                            type: 'string',
                                            title: '名称'
                                        },
                                        value: {
                                            type: 'string',
                                            title: '值'
                                        }
                                    },
                                    required: ['key', 'value']
                                }
                            }
                        }
                    },
                    uiSchema: {
                        headers: {
                            'ui:options': {
                                addable: true,
                                removable: true,
                                orderable: false
                            },
                            items: {
                                'ui:options': {
                                    inline: true
                                },
                                key: {
                                    'ui:placeholder': '名称',
                                    'ui:label': false,
                                    'ui:col': 4
                                },
                                value: {
                                    'ui:placeholder': '值',
                                    'ui:label': false,
                                    'ui:col': 8
                                }
                            }
                        }
                    }
                });

                if (data) {
                    // 处理headers字段 - 从数组转换为对象
                    let headers = {};
                    if (Array.isArray(data.headers)) {
                        data.headers.forEach((header: { key: string; value: string }) => {
                            if (header.key && header.value) {
                                headers[header.key] = header.value;
                            }
                        });
                    }

                    addMcpServer({
                        name: data.name,
                        url: data.url,
                        ...(data.type !== 'streamable_http' && { type: data.type }),
                        ...(Object.keys(headers).length > 0 && { headers })
                    });
                }
            }}
            className='d-flex align-items-center'
        >
            <GoPlus className='me-1' />
            添加 MCP 服务器
        </Button>
    </div>;

    return <Modal {...props} title={'工具配置'} size='lg' scrollable footer={footer}>
        <ToolsContainer>
            {/* 内置工具区域 */}
            <BuiltinTools />
            {/* MCP 服务器区域 */}
            {mcpServers.map(server => {
                return <McpServerItem key={server.name} server={server} />;
            })}
        </ToolsContainer>
    </Modal>;
}

const BuiltinTools = () => {
    const { builtinTools: tools } = useContext();
    const [expanded, setExpanded] = useState(true);

    const allToolNames = tools.map(tool => `${tool.plugin}.${tool.name}`);
    const {
        actualEnabledTools,
        enabledCount,
        toggleAllTools,
        toggleTool
    } = useToolsToggle('builtin-enabled-tools', allToolNames);

    return <ToolGroupContainer>
        <GroupHeader $expanded={expanded} onClick={() => setExpanded(!expanded)}>
            <GroupHeaderLeft>
                {expanded ? <BsChevronDown /> : <BsChevronRight />}
                <FormCheck
                    type='checkbox'
                    checked={enabledCount === tools.length}
                    ref={(el: HTMLInputElement | null) => {
                        if (el) {
                            el.indeterminate = enabledCount > 0 && enabledCount < tools.length;
                        }
                    }}
                    onClick={(e) => e.stopPropagation()}
                    onChange={(e) => {
                        e.stopPropagation();
                        toggleAllTools(e.target.checked);
                    }}
                    title={enabledCount === tools.length ? '全部关闭' : enabledCount === 0 ? '全部开启' : '全部开启'}
                />
                <GroupTitle>内置工具</GroupTitle>
                <ToolCount>({enabledCount}/{tools.length})</ToolCount>
            </GroupHeaderLeft>
        </GroupHeader>
        {expanded && (
            <ToolsList>
                {tools.map(tool => (
                    <ToolItem key={tool.name} className='tool-row'>
                        <ToolToggle>
                            <FormCheck
                                type='checkbox'
                                checked={actualEnabledTools.includes(`${tool.plugin}.${tool.name}`)}
                                onChange={() => toggleTool(`${tool.plugin}.${tool.name}`)}
                            />
                        </ToolToggle>
                        <ToolName>{tool.title}</ToolName>
                        <ToolDescription>{tool.description}</ToolDescription>
                    </ToolItem>
                ))}
            </ToolsList>
        )}
    </ToolGroupContainer>;
};

const McpServerItem = ({ server }: { server: McpServer }) => {
    const { deleteMcpServer, getMcpTools } = useContext();
    const [expanded, setExpanded] = useState(false);

    const { result: tools = [], loading } = useAsync(() => {
        return getMcpTools(server);
    }, [server]);

    const allToolNames = tools.map(tool => tool.name);
    const {
        actualEnabledTools,
        enabledCount,
        toggleAllTools,
        toggleTool
    } = useToolsToggle(`mcp-${server.name}-enabled-tools`, allToolNames);

    return (
        <ToolGroupContainer key={server.name}>
            <GroupHeader $expanded={expanded} onClick={() => {
                if (!loading) {
                    setExpanded(!expanded);
                }
            }}>
                <GroupHeaderLeft>
                    {expanded ? <BsChevronDown /> : <BsChevronRight />}
                    <FormCheck
                        type='checkbox'
                        checked={enabledCount === tools.length}
                        ref={(el: HTMLInputElement | null) => {
                            if (el) {
                                el.indeterminate = enabledCount > 0 && enabledCount < tools.length;
                            }
                        }}
                        onClick={(e) => e.stopPropagation()}
                        onChange={(e) => {
                            e.stopPropagation();
                            toggleAllTools(e.target.checked);
                        }}
                        title={enabledCount === tools.length ? '全部关闭' : enabledCount === 0 ? '全部开启' : '全部开启'}
                    />
                    <GroupTitle>{server.name}</GroupTitle>
                    <ToolCount>
                        {loading ?
                            <Spinner animation='border' variant={'primary'} size='sm' /> : `(${enabledCount}/${tools.length})`}
                    </ToolCount>
                </GroupHeaderLeft>
                <GroupActions onClick={e => e.stopPropagation()}>
                    <DeleteButton
                        onClick={() => {
                            deleteMcpServer(server.name);
                        }}
                        title='删除服务器'
                    >
                        <BsTrash />
                    </DeleteButton>
                </GroupActions>
            </GroupHeader>
            {expanded && !loading && <ToolsList>
                {tools.map(tool => (
                    <ToolItem key={tool.name} className='tool-row'>
                        <ToolToggle>
                            <FormCheck
                                type='checkbox'
                                checked={actualEnabledTools.includes(tool.name)}
                                onChange={() => toggleTool(tool.name)}
                            />
                        </ToolToggle>
                        <ToolName>{tool.title || tool.name}</ToolName>
                        <ToolDescription>{tool.description}</ToolDescription>
                    </ToolItem>
                ))}
            </ToolsList>}
        </ToolGroupContainer>
    );
};

const ToolsContainer = styled.div`
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
`;

const ToolGroupContainer = styled.div`
    border: 1px solid var(--bs-border-color);
    border-radius: var(--bs-border-radius);
    overflow: hidden;
`;

const GroupHeader = styled.div<{ $expanded?: boolean }>`
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.375rem 0.5rem;
    background: var(--ttw-foreground);
    cursor: pointer;
    border-bottom: ${props => props.$expanded ? '1px solid var(--bs-border-color)' : 'none'};

    &:hover {
        background: var(--ttw-box-hover-background);
    }
`;

const GroupHeaderLeft = styled.div`
    display: flex;
    align-items: center;
    gap: 0.375rem;
`;

const GroupTitle = styled.div`
    margin: 0;
    font-weight: 600;
    font-size: 1rem;
`;

const ToolCount = styled.span`
    color: var(--bs-secondary);
    font-size: 1rem;
`;

const GroupActions = styled.div`
    display: flex;
    align-items: center;
`;

const ToolsList = styled.div`
    position: relative;
    padding: 0.125rem 0.25rem 0.25rem;
    display: grid;
    grid-template-columns: auto max-content 1fr;
    align-items: stretch;
`;

const ToolItem = styled.div`
    display: contents;

    &:hover > * {
        background: var(--ttw-box-hover-background);
    }

    > * {
        padding: 0.25rem 0.5rem;
        height: 100%;
        display: flex;
        align-items: center;
    }

    > *:first-child {
        border-radius: var(--bs-border-radius) 0 0 var(--bs-border-radius);
    }

    > *:last-child {
        border-radius: 0 var(--bs-border-radius) var(--bs-border-radius) 0;
    }

    > *:only-child {
        border-radius: var(--bs-border-radius);
    }
`;

const ToolName = styled.div`
    font-weight: 500;
    font-size: 1rem;
    white-space: nowrap;
`;

const ToolDescription = styled.div`
    font-size: 1rem;
    color: var(--bs-secondary);
    line-height: 1.4;
    word-wrap: break-word;
`;

const ToolToggle = styled.div`
    display: flex;
    align-items: center;
    justify-content: center;
`;

const DeleteButton = styled.button`
    background: none;
    border: none;
    color: var(--bs-secondary);
    cursor: pointer;
    padding: 0.25rem;
    margin-left: 0.5rem;
    border-radius: var(--bs-border-radius);
    display: flex;
    align-items: center;

    &:hover {
        color: var(--bs-danger);
        background: var(--ttw-box-hover-background);
    }
`;
